import {faker} from '@faker-js/faker'
import axios from 'axios'
import {config} from 'dotenv'
import Strapi from 'strapi-sdk-js'
// import webpush from 'web-push'
import {generateRandomMedicalStudentData} from './generateStudents.js'
import {getAddress} from './getAddress.js'
import {medicalMissions} from './medicalMissions.js'

config({path: '../.env'})

if (!process.env.STRAPI_TOKEN) {
  console.error('❌ STRAPI_TOKEN is not defined in your .env file')
  console.log('Please add: STRAPI_TOKEN=your_api_token_here')
  process.exit(1)
}

console.log('✅ Using STRAPI_TOKEN:', process.env.STRAPI_TOKEN.substring(0, 20) + '...')

const strapi = new Strapi({
  url: 'http://localhost:1337',
  prefix: '/api',
})
strapi.setToken(process.env.STRAPI_TOKEN)

const total = 25

const createRandomStudents = async () => {
  try {
    console.log('Starting random student creation...')

    const studentData = generateRandomMedicalStudentData()

    for (let i = 0; i < total; i++) {
      const percent = Math.floor((i / total) * 100)
      const bar = '█'.repeat(percent / 2) + '▒'.repeat(50 - percent / 2)
      process.stdout.write(`\r[${bar}] ${percent}% | Student ${i + 1}/${total}`)

      const data = studentData[i]
      const firstName = faker.person.firstName()
      const lastName = faker.person.lastName()
      const email = faker.internet.email({
        firstName,
        lastName,
        provider: 'unil-fake.ch'
      }).toLowerCase()

      const studentPayload = {
        username: `${data.phone}@studentspool.ch`,
        email: email,
        password: 'TempPassword123!',
        confirmed: true,
        blocked: false,
        role: 3, // Student role ID
        first_name: firstName,
        last_name: lastName,
        biography: data.biography,
        birth_date: faker.date.birthdate({mode: 'age', min: 18, max: 28}),
        phoneNumber: data.phone,
        location: data.location,
        study_start_year: faker.date.between({from: '2018-01-01', to: '2023-12-31'}),
        specialty: faker.helpers.arrayElement([
          'Médecine générale',
          'Chirurgie',
          'Pédiatrie',
          'Cardiologie',
          'Neurologie',
          'Psychiatrie',
          'Radiologie'
        ]),
        driving_licence: faker.datatype.boolean(),
        has_vehicle: faker.datatype.boolean(),
        general_availability: faker.helpers.arrayElement([
          'Temps plein',
          'Temps partiel',
          'Weekends uniquement',
          'Soirées uniquement'
        ]),
        preferred_work_times: faker.helpers.arrayElement([
          'Matin',
          'Après-midi',
          'Soirée',
          'Nuit',
          'Aucune préférence'
        ]),
        weekend_availability: faker.datatype.boolean(),
        email_notifications: true,
        approved: faker.datatype.boolean(),
        status: faker.helpers.arrayElement(['pending', 'approved', 'rejected']),
        experiences: data.experiences,
        education: data.education
      }

      await strapi.request('POST', '/users', {
        headers: {
          'Authorization': `Bearer ${process.env.STRAPI_TOKEN}`,
          'Content-Type': 'application/json'
        },
        data: studentPayload
      })
    }

    console.log('\n✅ Random student creation completed!')
  } catch (e) {
    console.error('❌ Error in createRandomStudents:', e)
    if (e.response?.data) {
      console.error('Response data:', e.response.data)
    }
  }
}

const updateStudent = async () => {
  try {
    console.log('Starting student update...')

    console.log('Fetching students...')
    const studentsResponse = await strapi.request('GET', '/users?populate=role', {
      headers: {
        'Authorization': `Bearer ${process.env.STRAPI_TOKEN}`
      }
    })

    console.log('Students response:', studentsResponse)
    const students = Array.isArray(studentsResponse) ? studentsResponse : (studentsResponse.data || [])
    console.log(`Found ${students.length} students`)

    const randomData = generateRandomMedicalStudentData()

    for (let i = 0; i < students.length; i++) {
      const student = students[i]
      if (!student.phone_number?.startsWith('+33')) {
        console.log('Updating student:', student.id)
        await strapi.request('PUT', `/users/${student.id}`, {
          headers: {
            'Authorization': `Bearer ${process.env.STRAPI_TOKEN}`,
            'Content-Type': 'application/json'
          },
          data: {
            birth_date: faker.date.birthdate({mode: 'age', min: 18, max: 28}),
            phone_number: randomData[i]?.phone || faker.phone.number({style: 'international'}),
            email: faker.internet
              .email({
                firstName: student.first_name,
                lastName: student.last_name,
                provider: 'unil-fake.ch',
              })
              .toLowerCase(),
            biography: randomData[i]?.biography,
            location: randomData[i]?.location,
          }
        })
      }
    }
    console.log('\nStudent update completed!')
  } catch (e) {
    console.error('❌ Error in updateStudent:', e)

    if (e.response?.status === 403) {
      console.log('\n🔧 PERMISSION FIX NEEDED:')
      console.log('1. Go to http://localhost:1337/admin')
      console.log('2. Navigate to Settings → API Tokens')
      console.log('3. Edit your API token')
      console.log('4. Set Token type to "Full access" OR')
      console.log('5. Set permissions for Users-permissions → User → find, update')
    }
  }
}

const uploadImageFromUrl = async (imageUrl, filename) => {
  try {
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
    })

    const formData = new FormData()
    const blob = new Blob([response.data], {type: 'image/png'})
    const file = new File([blob], filename, {type: 'image/png'})
    formData.append('files', file)

    const uploadResult = await strapi.request('POST', '/upload', formData)
    return uploadResult[0]
  } catch (error) {
    console.error('Error uploading image:', error)
    return null
  }
}

const createMission = async () => {
  try {
    await strapi.login({
      identifier: '<EMAIL>',
      password: process.env.STRAPI_PASSWORD,
    })
    console.log('Starting mission creation...')

    for (let i = 0; i < medicalMissions.length; i++) {
      const percent = Math.floor((i / medicalMissions.length) * 100)
      const bar = '█'.repeat(percent / 2) + '▒'.repeat(50 - percent / 2)
      process.stdout.write(`\r[${bar}] ${percent}% | Mission ${i + 1}/${medicalMissions.length}`)

      const coverUrl = faker.image.urlPicsumPhotos({width: 256, height: 256})
      const coverFile = await uploadImageFromUrl(coverUrl, `mission-${i}-cover.png`)

      const startDate = faker.date.future({years: 3 / 12})
      const city = 'Lausanne'
      const address = faker.location.streetAddress()
      const country = 'Switzerland'
      const latitude = faker.location.latitude({min: 46.515, max: 46.525})
      const longitude = faker.location.longitude({min: 6.63, max: 6.64})

      const addressData = getAddress(longitude, latitude, address, city, country)
      const createdAddress = await strapi.create('addresses', {
        street: address,
        city: city,
        country: country,
        coordinates: {
          latitude: latitude,
          longitude: longitude,
        },
      })

      const missionContent = medicalMissions[i]
      const item = {
        title: missionContent.title,
        description: missionContent.description,
        start_date: startDate,
        end_date: faker.date.future({years: 4 / 12}),
        cover: coverFile?.id,
        address: createdAddress.data.id,
        status: 'published',
        candidates_needed: faker.number.int({min: 1, max: 10}),
      }
      await strapi.create('missions', item)
    }
    console.log('\nMission creation completed!')
  } catch (e) {
    console.error(e)
  }
}

const createApplications = async () => {
  try {
    console.log('Starting applications creation...')

    const studentsResponse = await strapi.find('users', {
      filters: {
        role: {
          name: {
            $eq: 'Student',
          },
        },
      },
      populate: ['role', 'avatar', 'documents'],
    })
    const students = studentsResponse.data

    const missionsResponse = await strapi.find('missions')
    const missions = missionsResponse.data

    const motivationalMessages = [
      'Je suis très motivé(e) à participer à cette mission car elle correspond parfaitement à mes aspirations dans le domaine médical.',
      "Cette opportunité représente pour moi une chance unique d'acquérir une expérience pratique précieuse.",
      "Mon intérêt pour ce domaine et ma volonté d'apprendre me poussent à postuler pour cette mission.",
      'Je souhaite mettre mes compétences au service de cette mission tout en développant mon expérience clinique.',
      "Cette mission correspond exactement à mon projet professionnel et je suis très enthousiaste à l'idée d'y participer.",
      'Je suis convaincu(e) que cette expérience sera enrichissante tant sur le plan professionnel que personnel.',
      "Mon parcours académique et mon désir d'apprentissage font de moi un(e) candidat(e) motivé(e) pour cette mission.",
      'Je suis passionné(e) par ce domaine et je souhaite contribuer activement à cette mission.',
    ]

    for (let i = 0; i < missions.length; i++) {
      const mission = missions[i]
      const percent = Math.floor((i / missions.length) * 100)
      const bar = '█'.repeat(percent / 2) + '▒'.repeat(50 - percent / 2)
      process.stdout.write(`\r[${bar}] ${percent}% | Mission ${i + 1}/${missions.length}`)

      const numApplications = faker.number.int({min: 5, max: 8})
      const shuffledStudents = [...students].sort(() => 0.5 - Math.random())
      const selectedStudents = shuffledStudents.slice(0, numApplications)

      for (const student of selectedStudents) {
        const application = {
          user: student.id,
          mission: mission.id,
          cover_letter: faker.helpers.arrayElement(motivationalMessages),
          applied_at: new Date(),
          status: 'pending',
        }

        await strapi.create('applications', application)
      }
    }

    console.log('\nApplications creation completed!')
  } catch (e) {
    console.error('Error creating applications:', e)
  }
}

const moveAllMissionsToJuly = async () => {
  try {
    await strapi.login({
      identifier: '<EMAIL>',
      password: process.env.STRAPI_PASSWORD,
    })
    console.log('Starting missions date update to July...')

    const missionsResponse = await strapi.find('missions')
    const missions = missionsResponse.data

    for (let i = 0; i < missions.length; i++) {
      const mission = missions[i]
      const percent = Math.floor((i / missions.length) * 100)
      const bar = '█'.repeat(percent / 2) + '▒'.repeat(50 - percent / 2)
      process.stdout.write(`\r[${bar}] ${percent}% | Mission ${i + 1}/${missions.length}`)

      const currentStartDate = new Date(mission.start_date)
      const currentEndDate = new Date(mission.end_date)
      const yearStart = currentStartDate.getFullYear()
      const yearEnd = currentEndDate.getFullYear()
      const julyStartDate = new Date(yearStart, 6, faker.number.int({min: 1, max: 31}))
      const julyEndDate = new Date(yearEnd, 6, faker.number.int({min: 1, max: 31}))

      await strapi.update('missions', mission.id, {
        start_date: julyStartDate,
        end_date: julyEndDate,
      })
    }

    console.log('\nAll missions updated to July!')
  } catch (e) {
    console.error('Error updating missions to July:', e)
  }
}

// webpush.setVapidDetails(
//   'mailto:<EMAIL>',
//   process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
//   process.env.VAPID_PRIVATE_KEY,
// )

export const sendNotification = async (subscription, data) => {
  console.log('Push notifications temporarily disabled')
  return false
  // try {
  //   await webpush.sendNotification(subscription, JSON.stringify(data))
  //   return true
  // } catch (error) {
  //   console.error('Error sending notification:', error)
  //   return false
  // }
}
;(async () => {
  await updateStudent()
  // await createMission()
  // await createApplications()
  // await moveAllMissionsToJuly()
  // const sub = {
  //   endpoint:
  //     'https://wns2-ln2p.notify.windows.com/w/?token=BQYAAAAcWqFSH9OVHA3GKYeZBpXC0Ge5oZer8FhcSdtRyMtOcuUKgbXNTbmqNjGjvjITKK0IJND47QivLdIgVDj4NR5GTDt2QodD8c9xKZrO%2bc1RMGjaSFdPzMcSRICd%2b1Vdte%2fzJRbyaMsMqemsHts175kg%2bJg7zP%2fUxhZ%2bLoUyuNkdtrtzKj66PSXlkaE6CSoJ%2bnFPaO5%2f3uCnVuT8oBfOoHwLP%2f8g24S5VTtZTqJahRUnOYP3Zlmh8WKvCM0AqhdwR35Rvu03j7OlEInr7I3nvdu338Q0xgfS9bmJ2VJ9MW%2bGcms3s%2f5%2bmTzSkKuaf8NUrT8%3d',
  //   expirationTime: null,
  //   keys: {
  //     p256dh:
  //       'BK5RCQ3XR6qPXQKHrTC0rmDBrs2B0EnEqBhVOoOt40M--dPpPsn-inBAkiVrYpDpxPn9--XwHmCRUnnXiTxNhUg',
  //     auth: 'xRJB26T299mgjpCvpCw0sA',
  //   },
  // }
  // await sendNotification(sub, {
  //   position: 'Hello!',
  //   body: 'This is a push notification from your PWA.',
  // })
})()
