export const generateRandomMedicalStudentData = () => {
  const biographies = [
    'Passionné par la science et le bien-être humain, je suis un étudiant en médecine déterminé à faire une différence positive dans la vie de mes patients.',
    "Animé par une soif d'apprendre et un profond respect pour le corps humain, je m'engage à devenir un médecin compétent et empathique.",
    "Curieux et rigoureux, je suis un étudiant en médecine qui aspire à maîtriser l'art de la guérison et à soulager la souffrance.",
    'Avec une approche holistique de la santé, je suis un futur médecin qui souhaite comprendre les patients dans leur globalité.',
    'Mon ambition est de devenir un médecin qui allie expertise scientifique et qualités humaines pour offrir les meilleurs soins possibles.',
    "Je suis un étudiant en médecine motivé par le défi intellectuel et l'opportunité d'aider les autres à vivre une vie plus saine.",
    "Guidé par une forte éthique professionnelle, je suis un futur médecin qui s'engage à respecter la dignité et l'autonomie de chaque patient.",
    'Mon parcours en médecine est une quête constante de connaissances et de compétences pour devenir un soignant dévoué et efficace.',
    "Je suis un étudiant en médecine qui croit en la puissance de la prévention et de l'éducation pour améliorer la santé de la communauté.",
    "Avec une grande capacité d'adaptation et un esprit d'équipe, je suis un futur médecin prêt à relever les défis du monde de la santé.",
    "Je suis un étudiant en médecine qui s'efforce de comprendre les mécanismes complexes du corps humain pour mieux le soigner.",
    'Mon objectif est de devenir un médecin qui inspire confiance et qui accompagne ses patients avec compassion et professionnalisme.',
    "Je suis un étudiant en médecine qui valorise l'importance de la communication et de l'écoute dans la relation soignant-soigné.",
    "Avec une approche scientifique et humaine, je suis un futur médecin qui souhaite contribuer à l'avancement de la médecine.",
    "Je suis un étudiant en médecine qui s'engage à respecter les principes de l'éthique médicale et à agir avec intégrité.",
    'Mon parcours en médecine est une aventure passionnante qui me permet de développer mes compétences et mon humanité.',
    "Je suis un étudiant en médecine qui aspire à devenir un leader dans le domaine de la santé et à promouvoir l'accès aux soins pour tous.",
    'Avec une grande curiosité intellectuelle, je suis un futur médecin qui souhaite explorer les nouvelles frontières de la médecine.',
    "Je suis un étudiant en médecine qui croit en la force de la collaboration et du travail d'équipe pour améliorer les soins de santé.",
    "Mon ambition est de devenir un médecin qui fait preuve d'empathie et qui prend en compte les besoins spécifiques de chaque patient.",
    "Je suis un étudiant en médecine qui s'engage à apprendre tout au long de sa carrière pour offrir les meilleurs soins possibles.",
    "Avec une approche pragmatique et une grande capacité d'analyse, je suis un futur médecin qui souhaite résoudre les problèmes de santé complexes.",
    "Je suis un étudiant en médecine qui valorise l'importance de la recherche et de l'innovation pour améliorer les traitements et les diagnostics.",
    "Mon parcours en médecine est une source d'inspiration et de motivation pour devenir un soignant compétent et dévoué.",
    "Je suis un étudiant en médecine qui s'engage à promouvoir la santé et le bien-être de la communauté.",
    'Avec une grande passion pour la médecine, je suis un futur médecin qui souhaite faire une différence positive dans la vie de ses patients.',
    "Je suis un étudiant en médecine qui s'efforce de développer ses compétences cliniques et son sens de l'observation.",
    'Mon objectif est de devenir un médecin qui inspire confiance et qui accompagne ses patients avec compassion et professionnalisme.',
    "Je suis un étudiant en médecine qui valorise l'importance de la communication et de l'écoute dans la relation soignant-soigné.",
    "Avec une approche scientifique et humaine, je suis un futur médecin qui souhaite contribuer à l'avancement de la médecine.",
    "Je suis un étudiant en médecine qui s'engage à respecter les principes de l'éthique médicale et à agir avec intégrité.",
    'Mon parcours en médecine est une aventure passionnante qui me permet de développer mes compétences et mon humanité.',
    "Je suis un étudiant en médecine qui aspire à devenir un leader dans le domaine de la santé et à promouvoir l'accès aux soins pour tous.",
    'Avec une grande curiosité intellectuelle, je suis un futur médecin qui souhaite explorer les nouvelles frontières de la médecine.',
    "Je suis un étudiant en médecine qui croit en la force de la collaboration et du travail d'équipe pour améliorer les soins de santé.",
    "Mon ambition est de devenir un médecin qui fait preuve d'empathie et qui prend en compte les besoins spécifiques de chaque patient.",
    "Je suis un étudiant en médecine qui s'engage à apprendre tout au long de sa carrière pour offrir les meilleurs soins possibles.",
    "Avec une approche pragmatique et une grande capacité d'analyse, je suis un futur médecin qui souhaite résoudre les problèmes de santé complexes.",
    "Je suis un étudiant en médecine qui valorise l'importance de la recherche et de l'innovation pour améliorer les traitements et les diagnostics.",
    "Mon parcours en médecine est une source d'inspiration et de motivation pour devenir un soignant compétent et dévoué.",
    "Je suis un étudiant en médecine qui s'engage à promouvoir la santé et le bien-être de la communauté.",
    'Avec une grande passion pour la médecine, je suis un futur médecin qui souhaite faire une différence positive dans la vie de ses patients.',
    "Je suis un étudiant en médecine qui s'efforce de développer ses compétences cliniques et son sens de l'observation.",
    'Mon objectif est de devenir un médecin qui inspire confiance et qui accompagne ses patients avec compassion et professionnalisme.',
    "Je suis un étudiant en médecine qui valorise l'importance de la communication et de l'écoute dans la relation soignant-soigné.",
    "Avec une approche scientifique et humaine, je suis un futur médecin qui souhaite contribuer à l'avancement de la médecine.",
    "Je suis un étudiant en médecine qui s'engage à respecter les principes de l'éthique médicale et à agir avec intégrité.",
    'Mon parcours en médecine est une aventure passionnante qui me permet de développer mes compétences et mon humanité.',
    "Je suis un étudiant en médecine qui aspire à devenir un leader dans le domaine de la santé et à promouvoir l'accès aux soins pour tous.",
    'Avec une grande curiosité intellectuelle, je suis un futur médecin qui souhaite explorer les nouvelles frontières de la médecine.',
  ]

  const degrees = [
    'Licence en Sciences de la Santé',
    'Master en Médecine',
    'Doctorat en Médecine',
    "Diplôme d'Études Spécialisées (DES)",
    'Diplôme Universitaire (DU)',
  ]

  const medicalFields = [
    'Médecine Générale',
    'Chirurgie',
    'Pédiatrie',
    'Cardiologie',
    'Oncologie',
    'Neurologie',
    'Psychiatrie',
    'Radiologie',
    'Gynécologie',
    'Dermatologie',
    'Ophtalmologie',
    'ORL',
    'Anesthésie-Réanimation',
    'Urgences',
    'Médecine Interne',
  ]

  const institutions = [
    'Université de Paris',
    'Université de Lyon',
    'Université de Marseille',
    'Université de Bordeaux',
    'Université de Lille',
    'Université de Montpellier',
    'Université de Strasbourg',
    'Université de Toulouse',
    'Université de Nantes',
    'Université de Nice',
    'Université de Rennes',
    'Université de Rouen',
    'Université de Reims',
    'Université de Tours',
    'Université de Grenoble',
  ]

  const companies = [
    'CHUV',
    'Hôpital Universitaire de Genève',
    'Hôpital de la Pitié-Salpêtrière',
    'Hôpital Saint-Louis',
    'Hôpital Necker-Enfants malades',
    'Hôpital Cochin',
    'Hôpital Lariboisière',
    'Hôpital Bichat-Claude Bernard',
    'Hôpital Tenon',
    'Hôpital Beaujon',
    'Hôpital Henri Mondor',
    'Hôpital Avicenne',
    'Hôpital Raymond Poincaré',
    'Hôpital Paul Brousse',
    'Hôpital Antoine Béclère',
  ]

  const descriptions = [
    'Participation aux soins des patients, observation clinique, assistance aux médecins.',
    'Réalisation de tâches administratives, gestion des dossiers patients, organisation des rendez-vous.',
    'Participation aux consultations, prise de notes, suivi des patients.',
    'Assistance lors des interventions chirurgicales, préparation du matériel, suivi post-opératoire.',
    'Participation aux gardes, gestion des urgences, prise en charge des patients en situation critique.',
    "Réalisation d'examens complémentaires, interprétation des résultats, rédaction de rapports.",
    "Participation aux réunions d'équipe, discussion des cas cliniques, élaboration des plans de traitement.",
    'Participation à des projets de recherche, collecte de données, analyse statistique.',
    'Participation à des campagnes de prévention, sensibilisation du public, éducation à la santé.',
    "Participation à des actions humanitaires, soins aux populations vulnérables, aide d'urgence.",
  ]

  const vaudAddresses = [
    'Rue de Lausanne 15, 1800 Vevey',
    'Avenue des Alpes 45, 1009 Pully',
    'Rue du Lac 23, 1400 Yverdon-les-Bains',
    'Route de Berne 7, 1010 Lausanne',
    'Avenue de la Gare 12, 1110 Morges',
    'Rue du Simplon 34, 1870 Monthey',
    'Avenue de Provence 8, 1007 Lausanne',
    'Rue du Château 5, 1820 Montreux',
    'Avenue de Cour 87, 1004 Lausanne',
    'Rue Centrale 25, 1450 Sainte-Croix',
    'Rue du Collège 3, 1800 Vevey',
    'Avenue de Rhodanie 58, 1006 Lausanne',
    'Rue de la Paix 11, 1020 Renens',
    'Avenue des Sports 20, 1400 Yverdon-les-Bains',
    'Rue du Midi 15, 1800 Vevey',
  ]

  const getRandomElement = array => array[Math.floor(Math.random() * array.length)]

  const getRandomDate = (startYear, endYear) => {
    const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear
    const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
    const day = String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const generateEducation = () => {
    const numEducations = Math.floor(Math.random() * 3) + 1
    const educationList = []
    let startYear = 2008
    for (let i = 0; i < numEducations; i++) {
      const endYear = startYear + Math.floor(Math.random() * 4) + 2
      educationList.push({
        degree: getRandomElement(degrees),
        field: getRandomElement(medicalFields),
        institution: getRandomElement(institutions),
        start_date: getRandomDate(startYear, startYear),
        end_date: getRandomDate(startYear, endYear),
      })
      startYear = endYear + 1
    }
    return educationList
  }

  const generateExperiences = () => {
    const numExperiences = Math.floor(Math.random() * 3) + 1
    const experienceList = []
    let startYear = 2018
    for (let i = 0; i < numExperiences; i++) {
      const endYear = startYear + Math.floor(Math.random() * 3) + 1
      experienceList.push({
        position: 'Stagiaire soin',
        company: getRandomElement(companies),
        start_date: getRandomDate(startYear, startYear),
        end_date: getRandomDate(startYear, endYear),
        description: getRandomElement(descriptions),
      })
      startYear = endYear + 1
    }
    return experienceList
  }

  const generateSwissPhoneNumber = () => {
    // Swiss mobile numbers start with +41 7x
    // Swiss landline numbers start with +41 2x or +41 3x
    const prefixes = ['21', '22', '24', '26', '27', '31', '32', '71', '76', '77', '78', '79']
    const prefix = getRandomElement(prefixes)

    // Generate 7 random digits
    const number = Math.floor(Math.random() * 10000000)
      .toString()
      .padStart(7, '0')

    return `+41${prefix}${number.slice(0, 3)}${number.slice(3, 5)}${number.slice(5)}`
  }
  const studentData = []
  for (let i = 0; i < 50; i++) {
    studentData.push({
      biography: getRandomElement(biographies),
      education: generateEducation(),
      experiences: generateExperiences(),
      location: getRandomElement(vaudAddresses),
      phone: generateSwissPhoneNumber(),
    })
  }
  return studentData
}
